2025-07-27 12:20:26,882 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:20:26,882 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:20:26,930 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:20:26,931 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:20:27,644 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:20:31,674 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:20:31,675 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:20:31,692 - INFO - 📄 File exports/brand_offices_updated.csv chưa tồn tại, bắt đầu từ đầu
2025-07-27 12:20:31,886 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:20:31,886 - INFO - ============================================================
2025-07-27 12:20:31,886 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:20:31,886 - INFO - ============================================================
2025-07-27 12:20:31,886 - INFO - 📊 Tổng records: 25989
2025-07-27 12:20:31,886 - INFO - 📊 Đã xử lý: 0
2025-07-27 12:20:31,886 - INFO - 📊 Còn lại: 25989
2025-07-27 12:20:31,886 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:20:31,886 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:20:31,888 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 0)
2025-07-27 12:20:31,889 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:20:31,892 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:31,892 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,895 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:31,896 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:31,898 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:31,899 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.668 10.8, 106.668 10.8, 106.668 10.8, 106.668 10.8, 106...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Nhuận', 'code': '27073'}
2025-07-27 12:20:31,900 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:31,900 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.668 10.8, 106.668 10.8, 106.668 10.8, 106.668 10.8, 106...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Nhuận', 'code': '27073'}
2025-07-27 12:20:31,900 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.66 10.764, 106.66 10.763, 106.66 10.762, 106.661 10.761...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Minh Phụng', 'code': '27238'}
2025-07-27 12:20:31,901 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.636 10.769, 106.636 10.769, 106.638 10.769, 106.64 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Thới', 'code': '27232'}
2025-07-27 12:20:31,901 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,902 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,902 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,903 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,904 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,904 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,904 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:31,905 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,905 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.684 10.777, 106.685 10.776, 106.685 10.776, 106.685 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bàn Cờ', 'code': '27154'}
2025-07-27 12:20:31,909 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.205 13.781, 109.205 13.781, 109.205 13.781, 109.205 13....>, 'geo_province_code': '52', 'province_title': 'Tỉnh Gia Lai', 'ward_title': 'Phường Quy Nhơn Nam', 'code': '21592'}
2025-07-27 12:20:31,910 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.103 10.943, 108.104 10.942, 108.104 10.942, 108.104 10....>, 'geo_province_code': '68', 'province_title': 'Tỉnh Lâm Đồng', 'ward_title': 'Phường Phan Thiết', 'code': '22945'}
2025-07-27 12:20:31,913 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:31,915 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:31,916 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.203 16.064, 108.203 16.064, 108.204 16.063, 108.204 16....>, 'geo_province_code': '48', 'province_title': 'Thành phố Đà Nẵng', 'ward_title': 'Phường Hòa Cường', 'code': '20257'}
2025-07-27 12:20:31,921 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.159 12.251, 109.16 12.251, 109.16 12.251, 109.161 12.25...>, 'geo_province_code': '56', 'province_title': 'Tỉnh Khánh Hòa', 'ward_title': 'Phường Nam Nha Trang', 'code': '22402'}
2025-07-27 12:20:31,924 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:31,925 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:31,925 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.685 10.768, 106.685 10.768, 106.686 10.768, 106.686 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Cầu Ông Lãnh', 'code': '26758'}
2025-07-27 12:20:31,926 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.636 10.769, 106.636 10.769, 106.638 10.769, 106.64 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Thới', 'code': '27232'}
2025-07-27 12:20:31,927 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.638 10.796, 106.638 10.796, 106.639 10.795, 106.639 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Phú', 'code': '27031'}
2025-07-27 12:20:31,928 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.827 10.76, 106.827 10.76, 106.827 10.76, 106.829 10.761...>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Xã Đại Phước', 'code': '26491'}
2025-07-27 12:20:31,929 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,931 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:31,931 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:31,932 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.661 10.742, 106.661 10.742, 106.661 10.742, 106.661 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Đông', 'code': '27424'}
2025-07-27 12:20:31,932 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,933 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.62 10.785, 106.621 10.785, 106.621 10.785, 106.621 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Thạnh', 'code': '27028'}
2025-07-27 12:20:31,944 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((107.542 16.482, 107.542 16.482, 107.542 16.482, 107.542 16....>, 'geo_province_code': '46', 'province_title': 'Thành phố Huế', 'ward_title': 'Phường Kim Long', 'code': '19774'}
2025-07-27 12:20:31,945 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,945 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.837 21.011, 105.837 21.011, 105.837 21.011, 105.838 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Kim Liên', 'code': '00229'}
2025-07-27 12:20:31,946 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,947 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:31,949 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:31,951 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:31,952 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,953 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.69 10.751, 106.69 10.75, 106.69 10.75, 106.69 10.75, 10...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Chánh Hưng', 'code': '27418'}
2025-07-27 12:20:31,953 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.698 10.76, 106.701 10.756, 106.701 10.756, 106.702 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vĩnh Hội', 'code': '27286'}
2025-07-27 12:20:31,954 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.675 10.761, 106.675 10.76, 106.676 10.759, 106.676 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường An Đông', 'code': '27316'}
2025-07-27 12:20:31,954 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.698 10.76, 106.701 10.756, 106.701 10.756, 106.702 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vĩnh Hội', 'code': '27286'}
2025-07-27 12:20:31,955 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.636 10.769, 106.636 10.769, 106.638 10.769, 106.64 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Thới', 'code': '27232'}
2025-07-27 12:20:31,955 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.684 10.777, 106.685 10.776, 106.685 10.776, 106.685 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bàn Cờ', 'code': '27154'}
2025-07-27 12:20:31,955 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:31,956 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.613 10.781, 106.613 10.78, 106.613 10.78, 106.613 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Trị Đông', 'code': '27448'}
2025-07-27 12:20:31,957 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.705 10.792, 106.706 10.792, 106.706 10.792, 106.706 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Sài Gòn', 'code': '26740'}
2025-07-27 12:20:31,957 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.705 10.792, 106.706 10.792, 106.706 10.792, 106.706 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Sài Gòn', 'code': '26740'}
2025-07-27 12:20:31,957 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,958 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,958 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.638 10.796, 106.638 10.796, 106.639 10.795, 106.639 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Phú', 'code': '27031'}
2025-07-27 12:20:31,959 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.649 10.809, 106.649 10.809, 106.65 10.809, 106.651 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bảy Hiền', 'code': '26983'}
2025-07-27 12:20:31,960 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.718 10.814, 106.718 10.814, 106.718 10.814, 106.718 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Thạnh Mỹ Tây', 'code': '26956'}
2025-07-27 12:20:31,961 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:31,962 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:31,963 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.691 10.811, 106.691 10.811, 106.691 10.811, 106.692 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Gia Định', 'code': '26944'}
2025-07-27 12:20:31,964 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:31,964 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:31,965 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,965 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:31,966 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:31,966 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:31,968 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.822 21.093, 105.826 21.092, 105.831 21.09, 105.831 21.0...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hồng Hà', 'code': '00097'}
2025-07-27 12:20:31,969 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,970 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:31,970 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:31,970 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:31,971 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,972 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,972 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:31,973 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.682 10.765, 106.682 10.765, 106.682 10.765, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Chợ Quán', 'code': '27301'}
2025-07-27 12:20:31,973 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.653 10.79, 106.653 10.79, 106.653 10.79, 106.653 10.789...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Hòa', 'code': '26995'}
2025-07-27 12:20:31,974 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.638 10.796, 106.638 10.796, 106.639 10.795, 106.639 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Phú', 'code': '27031'}
2025-07-27 12:20:31,975 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,975 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:31,976 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:31,977 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 20.988, 105.827 20.987, 105.827 20.987, 105.827 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Định Công', 'code': '00316'}
2025-07-27 12:20:31,978 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,978 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.891 21.051, 105.891 21.051, 105.892 21.051, 105.892 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Long Biên', 'code': '00145'}
2025-07-27 12:20:31,979 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,979 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.851 21.041, 105.851 21.04, 105.851 21.04, 105.851 21.04...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hoàn Kiếm', 'code': '00070'}
2025-07-27 12:20:31,980 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.77 20.965, 105.77 20.965, 105.77 20.965, 105.77 20.964,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Kiến Hưng', 'code': '09552'}
2025-07-27 12:20:31,981 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.851 21.041, 105.851 21.04, 105.851 21.04, 105.851 21.04...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hoàn Kiếm', 'code': '00070'}
2025-07-27 12:20:31,982 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.685 10.768, 106.685 10.768, 106.686 10.768, 106.686 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Cầu Ông Lãnh', 'code': '26758'}
2025-07-27 12:20:31,982 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,982 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:31,983 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.682 10.791, 106.682 10.791, 106.682 10.791, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Nhiêu Lộc', 'code': '27142'}
2025-07-27 12:20:31,983 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.701 10.765, 106.701 10.765, 106.702 10.764, 106.704 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Khánh Hội', 'code': '27265'}
2025-07-27 12:20:31,984 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,984 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.691 10.811, 106.691 10.811, 106.691 10.811, 106.692 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Gia Định', 'code': '26944'}
2025-07-27 12:20:31,985 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.668 10.8, 106.668 10.8, 106.668 10.8, 106.668 10.8, 106...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Nhuận', 'code': '27073'}
2025-07-27 12:20:31,986 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.684 10.834, 106.684 10.834, 106.684 10.834, 106.685 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hạnh Thông', 'code': '26890'}
2025-07-27 12:20:31,986 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.653 10.79, 106.653 10.79, 106.653 10.79, 106.653 10.789...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Hòa', 'code': '26995'}
2025-07-27 12:20:31,988 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:31,989 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.649 10.997, 106.65 10.997, 106.65 10.997, 106.651 10.99...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Thủ Dầu Một', 'code': '25747'}
2025-07-27 12:20:31,992 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.802 10.977, 106.802 10.977, 106.802 10.977, 106.802 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Trấn Biên', 'code': '26041'}
2025-07-27 12:20:31,997 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.159 12.251, 109.16 12.251, 109.16 12.251, 109.161 12.25...>, 'geo_province_code': '56', 'province_title': 'Tỉnh Khánh Hòa', 'ward_title': 'Phường Nam Nha Trang', 'code': '22402'}
2025-07-27 12:20:31,998 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.603 10.826, 106.604 10.826, 106.604 10.826, 106.606 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hưng Hòa', 'code': '27439'}
2025-07-27 12:20:31,998 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.62 10.785, 106.621 10.785, 106.621 10.785, 106.621 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Thạnh', 'code': '27028'}
2025-07-27 12:20:31,999 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.664 10.777, 106.667 10.768, 106.667 10.768, 106.669 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Diên Hồng', 'code': '27169'}
2025-07-27 12:20:31,999 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,999 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.698 10.76, 106.701 10.756, 106.701 10.756, 106.702 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vĩnh Hội', 'code': '27286'}
2025-07-27 12:20:32,000 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,001 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:32,002 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,002 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:32,003 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.891 21.051, 105.891 21.051, 105.892 21.051, 105.892 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Long Biên', 'code': '00145'}
2025-07-27 12:20:32,004 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.842 21.029, 105.843 21.028, 105.843 21.028, 105.843 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Cửa Nam', 'code': '00082'}
2025-07-27 12:20:32,004 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.121 9.208, 105.122 9.207, 105.123 9.206, 105.123 9.206,...>, 'geo_province_code': '96', 'province_title': 'Tỉnh Cà Mau', 'ward_title': 'Phường Lý Văn Lâm', 'code': '32014'}
2025-07-27 12:20:32,005 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.203 16.064, 108.203 16.064, 108.204 16.063, 108.204 16....>, 'geo_province_code': '48', 'province_title': 'Thành phố Đà Nẵng', 'ward_title': 'Phường Hòa Cường', 'code': '20257'}
2025-07-27 12:20:32,006 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.753 10.046, 105.753 10.046, 105.754 10.046, 105.755 10....>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Tân An', 'code': '31147'}
2025-07-27 12:20:32,009 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,009 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:32,010 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,011 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.66 18.697, 105.661 18.697, 105.661 18.696, 105.661 18.6...>, 'geo_province_code': '40', 'province_title': 'Tỉnh Nghệ An', 'ward_title': 'Phường Thành Vinh', 'code': '16681'}
2025-07-27 12:20:32,012 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:32,015 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.802 10.977, 106.802 10.977, 106.802 10.977, 106.802 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Trấn Biên', 'code': '26041'}
2025-07-27 12:20:32,018 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:32,018 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,019 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,019 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,019 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,020 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:32,021 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:32,022 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,022 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:32,023 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:32,023 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,031 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.86 18.344, 105.86 18.344, 105.861 18.344, 105.861 18.34...>, 'geo_province_code': '42', 'province_title': 'Tỉnh Hà Tĩnh', 'ward_title': 'Phường Hà Huy Tập', 'code': '18652'}
2025-07-27 12:20:32,032 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.467 9.784, 105.467 9.784, 105.467 9.784, 105.468 9.784,...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Vị Thanh', 'code': '31321'}
2025-07-27 12:20:32,032 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.995 10.264, 105.995 10.264, 105.996 10.264, 105.996 10....>, 'geo_province_code': '86', 'province_title': 'Tỉnh Vĩnh Long', 'ward_title': 'Phường Thanh Đức', 'code': '29590'}
2025-07-27 12:20:32,038 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((104.843 21.713, 104.845 21.712, 104.849 21.712, 104.852 21....>, 'geo_province_code': '15', 'province_title': 'Tỉnh Lào Cai', 'ward_title': 'Phường Âu Lâu', 'code': '04543'}
2025-07-27 12:20:32,046 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.097 12.696, 108.097 12.696, 108.097 12.696, 108.097 12....>, 'geo_province_code': '66', 'province_title': 'Tỉnh Đắk Lắk', 'ward_title': 'Phường Tân Lập', 'code': '24121'}
2025-07-27 12:20:32,047 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.649 10.997, 106.65 10.997, 106.65 10.997, 106.651 10.99...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Thủ Dầu Một', 'code': '25747'}
2025-07-27 12:20:32,049 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:32,049 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,050 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.822 21.093, 105.826 21.092, 105.831 21.09, 105.831 21.0...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hồng Hà', 'code': '00097'}
2025-07-27 12:20:32,051 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,052 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:32,054 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:32,057 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,058 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:32,058 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:32,061 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.775 10.017, 105.776 10.016, 105.776 10.016, 105.777 10....>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Cái Răng', 'code': '31186'}
2025-07-27 12:20:32,062 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,062 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,063 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:32,066 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,066 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,066 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,070 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,070 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,071 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,071 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:32,072 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.661 10.742, 106.661 10.742, 106.661 10.742, 106.661 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Đông', 'code': '27424'}
2025-07-27 12:20:32,073 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:32,073 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.603 10.826, 106.604 10.826, 106.604 10.826, 106.606 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hưng Hòa', 'code': '27439'}
2025-07-27 12:20:32,073 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.644 10.811, 106.644 10.811, 106.645 10.81, 106.645 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Bình', 'code': '27004'}
2025-07-27 12:20:32,074 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,074 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,075 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.715 10.738, 106.716 10.738, 106.717 10.738, 106.72 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Mỹ', 'code': '27487'}
2025-07-27 12:20:32,076 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.859 21.009, 105.859 21.009, 105.86 21.009, 105.861 21.0...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Bạch Mai', 'code': '00292'}
2025-07-27 12:20:32,076 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,077 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.203 16.064, 108.203 16.064, 108.204 16.063, 108.204 16....>, 'geo_province_code': '48', 'province_title': 'Thành phố Đà Nẵng', 'ward_title': 'Phường Hòa Cường', 'code': '20257'}
2025-07-27 12:20:32,082 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.159 12.251, 109.16 12.251, 109.16 12.251, 109.161 12.25...>, 'geo_province_code': '56', 'province_title': 'Tỉnh Khánh Hòa', 'ward_title': 'Phường Nam Nha Trang', 'code': '22402'}
2025-07-27 12:20:32,083 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:32,084 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.842 21.029, 105.843 21.028, 105.843 21.028, 105.843 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Cửa Nam', 'code': '00082'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.811 21.01, 105.811 21.01, 105.811 21.01, 105.813 21.009...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Thanh Xuân', 'code': '00367'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,086 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,087 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,087 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,087 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,087 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:20:33,718 - INFO - 🚀 Xử lý 400 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:36,209 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:21:36,209 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:21:36,245 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:21:36,245 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:21:36,594 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:21:41,093 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:21:41,095 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:21:41,111 - INFO - 📄 File exports/brand_offices_updated.csv chưa tồn tại, bắt đầu từ đầu
2025-07-27 12:21:41,273 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:21:41,273 - INFO - ============================================================
2025-07-27 12:21:41,273 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:21:41,273 - INFO - ============================================================
2025-07-27 12:21:41,273 - INFO - 📊 Tổng records: 25989
2025-07-27 12:21:41,273 - INFO - 📊 Đã xử lý: 0
2025-07-27 12:21:41,273 - INFO - 📊 Còn lại: 25989
2025-07-27 12:21:41,273 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:21:41,273 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:21:41,275 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 0)
2025-07-27 12:21:41,275 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:21:41,347 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:21:42,806 - INFO - 🚀 Xử lý 268 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:45,640 - INFO - 📊 Progress: 200 requests | 1272.4 RPM | 100.0% success
2025-07-27 12:21:48,715 - INFO - 📊 Batch RPM: 2721.3 RPM (268 records trong 5.91s)
2025-07-27 12:21:48,715 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 12:21:48,715 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 12:21:48,720 - INFO - ✅ Đã tạo file mới và lưu 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:21:48,720 - INFO - 😴 Xử lý batch trong 7.45s, nghỉ 53.553420066833496 giây để tránh rate limit...
2025-07-27 12:21:48,720 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 12:21:48,759 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 400)
2025-07-27 12:21:48,759 - INFO - 📊 Batch 2: 400 records
2025-07-27 12:21:48,819 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:21:49,400 - INFO - 🚀 Xử lý 271 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:51,520 - INFO - 📊 Progress: 400 requests | 1567.5 RPM | 100.0% success
2025-07-27 12:21:55,101 - INFO - 📊 Batch RPM: 2852.3 RPM (271 records trong 5.70s)
2025-07-27 12:21:55,101 - INFO - 🔄 Đã xử lý 800 records...
2025-07-27 12:21:55,101 - INFO - ✅ Hoàn thành batch 2: 400 records
2025-07-27 12:21:55,105 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:21:55,105 - INFO - 😴 Xử lý batch trong 6.38s, nghỉ 54.6150860786438 giây để tránh rate limit...
2025-07-27 12:21:55,105 - INFO - 🔄 Xử lý batch 3 (offset: 800)
2025-07-27 12:21:55,131 - INFO - 📊 Lấy được 400 records brand_office (offset: 800, excluded: 800)
2025-07-27 12:21:55,131 - INFO - 📊 Batch 3: 400 records
2025-07-27 12:21:55,192 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:21:55,586 - INFO - 🚀 Xử lý 267 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:56,156 - INFO - 📊 Progress: 600 requests | 1804.8 RPM | 100.0% success
2025-07-27 12:22:00,847 - INFO - 📊 Progress: 800 requests | 1948.2 RPM | 100.0% success
2025-07-27 12:22:00,884 - INFO - 📊 Batch RPM: 3023.8 RPM (267 records trong 5.30s)
2025-07-27 12:22:00,884 - INFO - 🔄 Đã xử lý 1200 records...
2025-07-27 12:22:00,884 - INFO - ✅ Hoàn thành batch 3: 400 records
2025-07-27 12:22:00,888 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:22:00,888 - INFO - 😴 Xử lý batch trong 5.78s, nghỉ 55.21640396118164 giây để tránh rate limit...
2025-07-27 12:22:00,888 - INFO - 🔄 Xử lý batch 4 (offset: 1200)
2025-07-27 12:22:00,917 - INFO - 📊 Lấy được 400 records brand_office (offset: 1200, excluded: 1200)
2025-07-27 12:22:00,917 - INFO - 📊 Batch 4: 400 records
2025-07-27 12:22:00,997 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:22:01,378 - INFO - 🚀 Xử lý 255 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:22:02,603 - INFO - 📊 Progress: 1000 requests | 2273.3 RPM | 100.0% success
2025-07-27 12:22:03,400 - INFO - 📊 Batch RPM: 7566.3 RPM (255 records trong 2.02s)
2025-07-27 12:22:03,401 - INFO - 🔄 Đã xử lý 1600 records...
2025-07-27 12:22:03,401 - INFO - ✅ Hoàn thành batch 4: 400 records
2025-07-27 12:22:03,404 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:22:03,404 - INFO - 😴 Xử lý batch trong 2.52s, nghỉ 58.48449206352234 giây để tránh rate limit...
2025-07-27 12:22:03,404 - INFO - 🔄 Xử lý batch 5 (offset: 1600)
2025-07-27 12:22:03,432 - INFO - 📊 Lấy được 400 records brand_office (offset: 1600, excluded: 1600)
2025-07-27 12:22:03,433 - INFO - 📊 Batch 5: 400 records
2025-07-27 12:22:03,706 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:22:04,118 - INFO - 🚀 Xử lý 229 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14285: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14277: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14329: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14322: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,157 - ERROR - ❌ Error processing record 14323: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,158 - ERROR - ❌ Error processing record 14280: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,161 - ERROR - ❌ Error processing record 14279: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,161 - ERROR - ❌ Error processing record 14272: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,162 - ERROR - ❌ Error processing record 14340: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,162 - ERROR - ❌ Error processing record 14282: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,162 - ERROR - ❌ Error processing record 14348: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14309: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14337: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14301: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14350: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14327: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14317: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14304: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14319: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14303: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14336: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,164 - ERROR - ❌ Error processing record 14305: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,164 - ERROR - ❌ Error processing record 14332: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,241 - ERROR - ❌ Error processing record 14366: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,246 - ERROR - ❌ Error processing record 14363: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,261 - ERROR - ❌ Error processing record 14368: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,293 - ERROR - ❌ Error processing record 14370: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,306 - INFO - 📊 Progress: 1200 requests | 2474.5 RPM | 100.0% success
2025-07-27 12:22:05,324 - ERROR - ❌ Error processing record 14385: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,326 - ERROR - ❌ Error processing record 14375: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,326 - ERROR - ❌ Error processing record 14371: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,327 - ERROR - ❌ Error processing record 14380: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,330 - ERROR - ❌ Error processing record 14379: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,356 - ERROR - ❌ Error processing record 14388: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,386 - ERROR - ❌ Error processing record 14389: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,397 - ERROR - ❌ Error processing record 14391: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,409 - ERROR - ❌ Error processing record 14392: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,415 - ERROR - ❌ Error processing record 14393: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,416 - ERROR - ❌ Error processing record 14395: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,418 - ERROR - ❌ Error processing record 14400: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,421 - ERROR - ❌ Error processing record 14402: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,425 - ERROR - ❌ Error processing record 14406: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,428 - ERROR - ❌ Error processing record 14405: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,448 - ERROR - ❌ Error processing record 14407: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,449 - ERROR - ❌ Error processing record 14409: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,452 - ERROR - ❌ Error processing record 14408: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,452 - ERROR - ❌ Error processing record 14410: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,453 - ERROR - ❌ Error processing record 14411: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,950 - INFO - 📊 Batch RPM: 7502.9 RPM (229 records trong 1.83s)
2025-07-27 12:22:05,950 - INFO - 🔄 Đã xử lý 2000 records...
2025-07-27 12:22:05,951 - INFO - ✅ Hoàn thành batch 5: 400 records
2025-07-27 12:22:05,956 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:22:05,956 - INFO - 😴 Xử lý batch trong 2.55s, nghỉ 58.44778108596802 giây để tránh rate limit...
2025-07-27 12:22:05,956 - INFO - 🔄 Xử lý batch 6 (offset: 2000)
2025-07-27 12:22:05,985 - INFO - 📊 Lấy được 400 records brand_office (offset: 2000, excluded: 2000)
2025-07-27 12:22:05,985 - INFO - 📊 Batch 6: 400 records
2025-07-27 12:22:06,198 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:22:06,437 - ERROR - ❌ Lỗi trong quá trình xử lý: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 53
}
]
2025-07-27 12:24:51,690 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:24:51,690 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:24:51,730 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:24:51,730 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:24:52,092 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:25:04,673 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:25:04,673 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:25:04,692 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:25:04,693 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:25:05,030 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:25:08,881 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:25:08,882 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:25:08,896 - INFO - 📄 File exports/brand_offices_updated.csv chưa tồn tại, bắt đầu từ đầu
2025-07-27 12:25:08,988 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:25:08,989 - INFO - ============================================================
2025-07-27 12:25:08,989 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:25:08,989 - INFO - ============================================================
2025-07-27 12:25:08,989 - INFO - 📊 Tổng records: 25989
2025-07-27 12:25:08,989 - INFO - 📊 Đã xử lý: 0
2025-07-27 12:25:08,989 - INFO - 📊 Còn lại: 25989
2025-07-27 12:25:08,989 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:25:08,989 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:25:08,991 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 0)
2025-07-27 12:25:08,991 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:25:09,057 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:25:20,643 - INFO - 🚀 Xử lý 268 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:25:23,422 - INFO - 📊 Progress: 200 requests | 640.0 RPM | 100.0% success
2025-07-27 12:25:27,136 - INFO - 📊 Batch RPM: 2476.8 RPM (268 records trong 6.49s)
2025-07-27 12:25:27,136 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 12:25:27,136 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 12:25:27,148 - INFO - ✅ Đã tạo file mới và lưu 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:25:27,148 - INFO - 😴 Xử lý batch trong 18.16s, nghỉ 42.84077787399292 giây để tránh rate limit...
2025-07-27 12:26:09,990 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 12:26:10,020 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 400)
2025-07-27 12:26:10,021 - INFO - 📊 Batch 2: 400 records
2025-07-27 12:26:10,088 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:26:10,620 - INFO - 🚀 Xử lý 271 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:26:44,978 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:26:44,979 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:26:45,006 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:26:45,006 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:26:45,383 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:26:49,274 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:26:49,275 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:26:49,296 - INFO - 📊 Đã đọc 400 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 12:26:49,376 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:26:49,376 - INFO - ============================================================
2025-07-27 12:26:49,376 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:26:49,376 - INFO - ============================================================
2025-07-27 12:26:49,376 - INFO - 📊 Tổng records: 25989
2025-07-27 12:26:49,376 - INFO - 📊 Đã xử lý: 400
2025-07-27 12:26:49,376 - INFO - 📊 Còn lại: 25589
2025-07-27 12:26:49,376 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:26:49,376 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:26:49,380 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 400)
2025-07-27 12:26:49,380 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:26:49,464 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:26:50,270 - INFO - 🚀 Xử lý 259 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:26:54,922 - INFO - 📊 Progress: 200 requests | 1206.9 RPM | 100.0% success
2025-07-27 12:26:59,528 - INFO - 📊 Batch RPM: 1678.6 RPM (259 records trong 9.26s)
2025-07-27 12:26:59,529 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 12:26:59,530 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 12:26:59,541 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:26:59,541 - INFO - 😴 Xử lý batch trong 10.17s, nghỉ 50.83455204963684 giây để tránh rate limit...
2025-07-27 12:27:50,378 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 12:27:50,435 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 800)
2025-07-27 12:27:50,435 - INFO - 📊 Batch 2: 400 records
2025-07-27 12:27:50,505 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:27:50,882 - INFO - 🚀 Xử lý 280 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:27:57,095 - INFO - 📊 Progress: 400 requests | 332.8 RPM | 100.0% success
2025-07-27 12:28:04,630 - INFO - 📊 Batch RPM: 1222.1 RPM (280 records trong 13.75s)
2025-07-27 12:28:04,631 - INFO - 🔄 Đã xử lý 800 records...
2025-07-27 12:28:04,631 - INFO - ✅ Hoàn thành batch 2: 400 records
2025-07-27 12:28:04,634 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:28:04,634 - INFO - 😴 Xử lý batch trong 14.25s, nghỉ 46.74600386619568 giây để tránh rate limit...
2025-07-27 12:28:51,383 - INFO - 🔄 Xử lý batch 3 (offset: 800)
2025-07-27 12:28:51,402 - INFO - 📊 Lấy được 400 records brand_office (offset: 800, excluded: 1200)
2025-07-27 12:28:51,402 - INFO - 📊 Batch 3: 400 records
2025-07-27 12:28:51,496 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:28:51,893 - INFO - 🚀 Xử lý 248 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:28:52,453 - INFO - 📊 Progress: 600 requests | 282.4 RPM | 100.0% success
2025-07-27 12:28:57,740 - INFO - 📊 Batch RPM: 2545.5 RPM (248 records trong 5.85s)
2025-07-27 12:28:57,740 - INFO - 🔄 Đã xử lý 1200 records...
2025-07-27 12:28:57,742 - INFO - ✅ Hoàn thành batch 3: 400 records
2025-07-27 12:28:57,754 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:28:57,755 - INFO - 😴 Xử lý batch trong 6.37s, nghỉ 54.63087606430054 giây để tránh rate limit...
2025-07-27 12:29:52,386 - INFO - 🔄 Xử lý batch 4 (offset: 1200)
2025-07-27 12:29:52,400 - INFO - 📊 Lấy được 400 records brand_office (offset: 1200, excluded: 1600)
2025-07-27 12:29:52,400 - INFO - 📊 Batch 4: 400 records
2025-07-27 12:29:52,601 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:29:52,994 - INFO - 🚀 Xử lý 245 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:29:53,544 - INFO - 📊 Progress: 800 requests | 254.6 RPM | 100.0% success
2025-07-27 12:29:54,677 - INFO - 📊 Progress: 1000 requests | 316.3 RPM | 100.0% success
2025-07-27 12:29:55,194 - INFO - 📊 Batch RPM: 6682.9 RPM (245 records trong 2.20s)
2025-07-27 12:29:55,195 - INFO - 🔄 Đã xử lý 1600 records...
2025-07-27 12:29:55,196 - INFO - ✅ Hoàn thành batch 4: 400 records
2025-07-27 12:29:55,203 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:29:55,203 - INFO - 😴 Xử lý batch trong 2.82s, nghỉ 58.184059858322144 giây để tránh rate limit...
2025-07-27 12:30:53,390 - INFO - 🔄 Xử lý batch 5 (offset: 1600)
2025-07-27 12:30:53,413 - INFO - 📊 Lấy được 400 records brand_office (offset: 1600, excluded: 2000)
2025-07-27 12:30:53,413 - INFO - 📊 Batch 5: 400 records
2025-07-27 12:30:53,490 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:30:53,876 - INFO - 🚀 Xử lý 238 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:30:56,027 - INFO - 📊 Progress: 1200 requests | 286.8 RPM | 100.0% success
2025-07-27 12:30:57,848 - INFO - 📊 Batch RPM: 3595.2 RPM (238 records trong 3.97s)
2025-07-27 12:30:57,849 - INFO - 🔄 Đã xử lý 2000 records...
2025-07-27 12:30:57,850 - INFO - ✅ Hoàn thành batch 5: 400 records
2025-07-27 12:30:57,860 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:30:57,860 - INFO - 😴 Xử lý batch trong 4.47s, nghỉ 56.53036069869995 giây để tránh rate limit...
2025-07-27 12:31:54,392 - INFO - 🔄 Xử lý batch 6 (offset: 2000)
2025-07-27 12:31:54,434 - INFO - 📊 Lấy được 400 records brand_office (offset: 2000, excluded: 2400)
2025-07-27 12:31:54,434 - INFO - 📊 Batch 6: 400 records
2025-07-27 12:31:54,673 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:31:55,131 - INFO - 🚀 Xử lý 238 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:31:56,221 - INFO - 📊 Progress: 1400 requests | 269.9 RPM | 100.0% success
2025-07-27 12:31:59,367 - INFO - 📊 Batch RPM: 3372.1 RPM (238 records trong 4.23s)
2025-07-27 12:31:59,368 - INFO - 🔄 Đã xử lý 2400 records...
2025-07-27 12:31:59,369 - INFO - ✅ Hoàn thành batch 6: 400 records
2025-07-27 12:31:59,378 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:31:59,378 - INFO - 😴 Xử lý batch trong 4.98s, nghỉ 56.01561617851257 giây để tránh rate limit...
2025-07-27 12:32:55,395 - INFO - 🔄 Xử lý batch 7 (offset: 2400)
2025-07-27 12:32:55,433 - INFO - 📊 Lấy được 400 records brand_office (offset: 2400, excluded: 2800)
2025-07-27 12:32:55,434 - INFO - 📊 Batch 7: 400 records
2025-07-27 12:32:55,659 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:32:56,097 - INFO - 🚀 Xử lý 216 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:32:57,148 - INFO - 📊 Progress: 1600 requests | 257.9 RPM | 100.0% success
2025-07-27 12:33:00,058 - INFO - 📊 Batch RPM: 3273.1 RPM (216 records trong 3.96s)
2025-07-27 12:33:00,059 - INFO - 🔄 Đã xử lý 2800 records...
2025-07-27 12:33:00,060 - INFO - ✅ Hoàn thành batch 7: 400 records
2025-07-27 12:33:00,064 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:33:00,064 - INFO - 😴 Xử lý batch trong 4.67s, nghỉ 56.33333992958069 giây để tránh rate limit...
2025-07-27 12:33:56,414 - INFO - 🔄 Xử lý batch 8 (offset: 2800)
2025-07-27 12:33:56,463 - INFO - 📊 Lấy được 400 records brand_office (offset: 2800, excluded: 3200)
2025-07-27 12:33:56,464 - INFO - 📊 Batch 8: 400 records
2025-07-27 12:33:56,647 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:33:57,079 - INFO - 🚀 Xử lý 255 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:33:57,774 - INFO - 📊 Progress: 1800 requests | 249.5 RPM | 100.0% success
2025-07-27 12:34:04,026 - INFO - 📊 Batch RPM: 2202.6 RPM (255 records trong 6.95s)
2025-07-27 12:34:04,027 - INFO - 🔄 Đã xử lý 3200 records...
2025-07-27 12:34:04,027 - INFO - ✅ Hoàn thành batch 8: 400 records
2025-07-27 12:34:04,034 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:34:04,035 - INFO - 😴 Xử lý batch trong 7.62s, nghỉ 53.38120985031128 giây để tránh rate limit...
2025-07-27 12:34:57,418 - INFO - 🔄 Xử lý batch 9 (offset: 3200)
2025-07-27 12:34:57,487 - INFO - 📊 Lấy được 400 records brand_office (offset: 3200, excluded: 3600)
2025-07-27 12:34:57,488 - INFO - 📊 Batch 9: 400 records
2025-07-27 12:34:57,577 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:34:57,974 - INFO - 🚀 Xử lý 254 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:34:58,548 - INFO - 📊 Progress: 2000 requests | 243.1 RPM | 100.0% success
2025-07-27 12:35:04,608 - INFO - 📊 Progress: 2200 requests | 264.2 RPM | 100.0% success
2025-07-27 12:35:06,870 - INFO - 📊 Batch RPM: 1713.2 RPM (254 records trong 8.90s)
2025-07-27 12:35:06,871 - INFO - 🔄 Đã xử lý 3600 records...
2025-07-27 12:35:06,872 - INFO - ✅ Hoàn thành batch 9: 400 records
2025-07-27 12:35:06,881 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:35:06,881 - INFO - 😴 Xử lý batch trong 9.46s, nghỉ 51.537439823150635 giây để tránh rate limit...
2025-07-27 12:35:58,422 - INFO - 🔄 Xử lý batch 10 (offset: 3600)
2025-07-27 12:35:58,473 - INFO - 📊 Lấy được 400 records brand_office (offset: 3600, excluded: 4000)
2025-07-27 12:35:58,473 - INFO - 📊 Batch 10: 400 records
2025-07-27 12:35:58,566 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:35:58,958 - INFO - 🚀 Xử lý 254 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:36:00,635 - INFO - 📊 Progress: 2400 requests | 259.2 RPM | 100.0% success
2025-07-27 12:36:06,508 - INFO - 📊 Batch RPM: 2018.8 RPM (254 records trong 7.55s)
2025-07-27 12:36:06,508 - INFO - 🔄 Đã xử lý 4000 records...
2025-07-27 12:36:06,509 - INFO - ✅ Hoàn thành batch 10: 400 records
2025-07-27 12:36:06,518 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:36:06,518 - INFO - 😴 Xử lý batch trong 8.09s, nghỉ 52.905786752700806 giây để tránh rate limit...
2025-07-27 12:36:59,426 - INFO - 🔄 Xử lý batch 11 (offset: 4000)
2025-07-27 12:36:59,473 - INFO - 📊 Lấy được 400 records brand_office (offset: 4000, excluded: 4400)
2025-07-27 12:36:59,474 - INFO - 📊 Batch 11: 400 records
2025-07-27 12:36:59,575 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:36:59,989 - INFO - 🚀 Xử lý 262 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:37:01,003 - INFO - 📊 Progress: 2600 requests | 253.2 RPM | 100.0% success
2025-07-27 12:37:05,860 - INFO - 📊 Batch RPM: 2677.9 RPM (262 records trong 5.87s)
2025-07-27 12:37:05,861 - INFO - 🔄 Đã xử lý 4400 records...
2025-07-27 12:37:05,866 - INFO - ✅ Hoàn thành batch 11: 400 records
2025-07-27 12:37:05,876 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:37:05,877 - INFO - 😴 Xử lý batch trong 6.45s, nghỉ 54.54958367347717 giây để tránh rate limit...
2025-07-27 12:38:00,429 - INFO - 🔄 Xử lý batch 12 (offset: 4400)
2025-07-27 12:38:00,483 - INFO - 📊 Lấy được 400 records brand_office (offset: 4400, excluded: 4800)
2025-07-27 12:38:00,483 - INFO - 📊 Batch 12: 400 records
2025-07-27 12:38:00,645 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:38:01,159 - INFO - 🚀 Xử lý 252 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:38:01,731 - INFO - 📊 Progress: 2800 requests | 248.2 RPM | 100.0% success
2025-07-27 12:38:03,884 - INFO - 📊 Progress: 3000 requests | 265.1 RPM | 100.0% success
2025-07-27 12:38:03,991 - INFO - 📊 Batch RPM: 5341.5 RPM (252 records trong 2.83s)
2025-07-27 12:38:03,991 - INFO - 🔄 Đã xử lý 4800 records...
2025-07-27 12:38:03,993 - INFO - ✅ Hoàn thành batch 12: 400 records
2025-07-27 12:38:04,002 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:38:04,003 - INFO - 😴 Xử lý batch trong 3.57s, nghỉ 57.42817831039429 giây để tránh rate limit...
2025-07-27 12:39:01,434 - INFO - 🔄 Xử lý batch 13 (offset: 4800)
2025-07-27 12:39:01,469 - INFO - 📊 Lấy được 400 records brand_office (offset: 4800, excluded: 5200)
2025-07-27 12:39:01,469 - INFO - 📊 Batch 13: 400 records
2025-07-27 12:39:01,592 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:39:02,008 - INFO - 🚀 Xử lý 258 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:39:04,276 - INFO - 📊 Progress: 3200 requests | 259.7 RPM | 100.0% success
2025-07-27 12:39:07,924 - INFO - 📊 Batch RPM: 2616.6 RPM (258 records trong 5.92s)
2025-07-27 12:39:07,925 - INFO - 🔄 Đã xử lý 5200 records...
2025-07-27 12:39:07,925 - INFO - ✅ Hoàn thành batch 13: 400 records
2025-07-27 12:39:07,938 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:39:07,938 - INFO - 😴 Xử lý batch trong 6.50s, nghỉ 54.49626183509827 giây để tránh rate limit...
2025-07-27 12:40:02,439 - INFO - 🔄 Xử lý batch 14 (offset: 5200)
2025-07-27 12:40:02,494 - INFO - 📊 Lấy được 400 records brand_office (offset: 5200, excluded: 5600)
2025-07-27 12:40:02,494 - INFO - 📊 Batch 14: 400 records
2025-07-27 12:40:02,605 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:40:03,009 - INFO - 🚀 Xử lý 261 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:40:04,598 - INFO - 📊 Progress: 3400 requests | 255.1 RPM | 100.0% success
2025-07-27 12:40:09,059 - INFO - 📊 Batch RPM: 2588.7 RPM (261 records trong 6.05s)
2025-07-27 12:40:09,060 - INFO - 🔄 Đã xử lý 5600 records...
2025-07-27 12:40:09,061 - INFO - ✅ Hoàn thành batch 14: 400 records
2025-07-27 12:40:09,069 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:40:09,069 - INFO - 😴 Xử lý batch trong 6.63s, nghỉ 54.37018704414368 giây để tránh rate limit...
2025-07-27 12:41:03,443 - INFO - 🔄 Xử lý batch 15 (offset: 5600)
2025-07-27 12:41:03,508 - INFO - 📊 Lấy được 400 records brand_office (offset: 5600, excluded: 6000)
2025-07-27 12:41:03,509 - INFO - 📊 Batch 15: 400 records
2025-07-27 12:41:03,623 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:41:04,031 - INFO - 🚀 Xử lý 251 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:41:04,663 - INFO - 📊 Progress: 3600 requests | 251.3 RPM | 100.0% success
2025-07-27 12:41:09,582 - INFO - 📊 Batch RPM: 2713.0 RPM (251 records trong 5.55s)
2025-07-27 12:41:09,583 - INFO - 🔄 Đã xử lý 6000 records...
2025-07-27 12:41:09,583 - INFO - ✅ Hoàn thành batch 15: 400 records
2025-07-27 12:41:09,588 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:41:09,589 - INFO - 😴 Xử lý batch trong 6.14s, nghỉ 54.8560471534729 giây để tránh rate limit...
2025-07-27 12:42:04,448 - INFO - 🔄 Xử lý batch 16 (offset: 6000)
2025-07-27 12:42:04,505 - INFO - 📊 Lấy được 400 records brand_office (offset: 6000, excluded: 6400)
2025-07-27 12:42:04,506 - INFO - 📊 Batch 16: 400 records
2025-07-27 12:42:04,632 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:42:05,065 - INFO - 🚀 Xử lý 257 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:42:05,614 - INFO - 📊 Progress: 3800 requests | 247.7 RPM | 100.0% success
2025-07-27 12:42:11,237 - INFO - 📊 Progress: 4000 requests | 259.1 RPM | 100.0% success
2025-07-27 12:42:12,713 - INFO - 📊 Batch RPM: 2016.3 RPM (257 records trong 7.65s)
2025-07-27 12:42:12,714 - INFO - 🔄 Đã xử lý 6400 records...
2025-07-27 12:42:12,714 - INFO - ✅ Hoàn thành batch 16: 400 records
2025-07-27 12:42:12,722 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:42:12,722 - INFO - 😴 Xử lý batch trong 8.27s, nghỉ 52.72720789909363 giây để tránh rate limit...
2025-07-27 12:43:05,455 - INFO - 🔄 Xử lý batch 17 (offset: 6400)
2025-07-27 12:43:05,511 - INFO - 📊 Lấy được 400 records brand_office (offset: 6400, excluded: 6800)
2025-07-27 12:43:05,512 - INFO - 📊 Batch 17: 400 records
2025-07-27 12:43:05,629 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:43:06,044 - INFO - 🚀 Xử lý 248 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:43:08,365 - INFO - 📊 Progress: 4200 requests | 256.3 RPM | 100.0% success
2025-07-27 12:43:12,351 - INFO - 📊 Batch RPM: 2359.6 RPM (248 records trong 6.31s)
2025-07-27 12:43:12,353 - INFO - 🔄 Đã xử lý 6800 records...
2025-07-27 12:43:12,355 - INFO - ✅ Hoàn thành batch 17: 400 records
2025-07-27 12:43:12,366 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:43:12,367 - INFO - 😴 Xử lý batch trong 6.91s, nghỉ 54.09070110321045 giây để tránh rate limit...
2025-07-27 12:44:06,458 - INFO - 🔄 Xử lý batch 18 (offset: 6800)
2025-07-27 12:44:06,517 - INFO - 📊 Lấy được 400 records brand_office (offset: 6800, excluded: 7200)
2025-07-27 12:44:06,517 - INFO - 📊 Batch 18: 400 records
2025-07-27 12:44:06,601 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:44:07,058 - INFO - 🚀 Xử lý 267 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:44:08,252 - INFO - 📊 Progress: 4400 requests | 253.0 RPM | 100.0% success
2025-07-27 12:44:13,604 - INFO - 📊 Batch RPM: 2447.4 RPM (267 records trong 6.55s)
2025-07-27 12:44:13,605 - INFO - 🔄 Đã xử lý 7200 records...
2025-07-27 12:44:13,609 - INFO - ✅ Hoàn thành batch 18: 400 records
2025-07-27 12:44:13,617 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:44:13,617 - INFO - 😴 Xử lý batch trong 7.16s, nghỉ 53.84280180931091 giây để tránh rate limit...
2025-07-27 12:45:07,465 - INFO - 🔄 Xử lý batch 19 (offset: 7200)
2025-07-27 12:45:07,560 - INFO - 📊 Lấy được 400 records brand_office (offset: 7200, excluded: 7600)
2025-07-27 12:45:07,561 - INFO - 📊 Batch 19: 400 records
2025-07-27 12:45:07,715 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:45:08,185 - INFO - 🚀 Xử lý 267 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:45:08,808 - INFO - 📊 Progress: 4600 requests | 250.0 RPM | 100.0% success
2025-07-27 12:45:16,216 - INFO - 📊 Progress: 4800 requests | 259.2 RPM | 100.0% success
2025-07-27 12:45:16,662 - INFO - 📊 Batch RPM: 1890.0 RPM (267 records trong 8.48s)
2025-07-27 12:45:16,662 - INFO - 🔄 Đã xử lý 7600 records...
2025-07-27 12:45:16,663 - INFO - ✅ Hoàn thành batch 19: 400 records
2025-07-27 12:45:16,673 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:45:16,673 - INFO - 😴 Xử lý batch trong 9.20s, nghỉ 51.795750856399536 giây để tránh rate limit...
2025-07-27 12:46:08,472 - INFO - 🔄 Xử lý batch 20 (offset: 7600)
2025-07-27 12:46:08,526 - INFO - 📊 Lấy được 400 records brand_office (offset: 7600, excluded: 8000)
2025-07-27 12:46:08,527 - INFO - 📊 Batch 20: 400 records
2025-07-27 12:46:08,594 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:46:09,020 - INFO - 🚀 Xử lý 272 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:46:12,061 - INFO - 📊 Progress: 5000 requests | 257.1 RPM | 100.0% success
2025-07-27 12:46:15,808 - INFO - 📊 Batch RPM: 2404.4 RPM (272 records trong 6.79s)
2025-07-27 12:46:15,809 - INFO - 🔄 Đã xử lý 8000 records...
2025-07-27 12:46:15,810 - INFO - ✅ Hoàn thành batch 20: 400 records
2025-07-27 12:46:15,819 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:46:15,819 - INFO - 😴 Xử lý batch trong 7.34s, nghỉ 53.65734815597534 giây để tránh rate limit...
2025-07-27 12:47:09,479 - INFO - 🔄 Xử lý batch 21 (offset: 8000)
2025-07-27 12:47:09,538 - INFO - 📊 Lấy được 400 records brand_office (offset: 8000, excluded: 8400)
2025-07-27 12:47:09,539 - INFO - 📊 Batch 21: 400 records
2025-07-27 12:47:09,704 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:47:10,088 - INFO - 🚀 Xử lý 238 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:47:11,134 - INFO - 📊 Progress: 5200 requests | 254.5 RPM | 100.0% success
2025-07-27 12:47:15,727 - INFO - 📊 Batch RPM: 2532.6 RPM (238 records trong 5.64s)
2025-07-27 12:47:15,728 - INFO - 🔄 Đã xử lý 8400 records...
2025-07-27 12:47:15,734 - INFO - ✅ Hoàn thành batch 21: 400 records
2025-07-27 12:47:15,743 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:47:15,744 - INFO - 😴 Xử lý batch trong 6.26s, nghỉ 54.73626112937927 giây để tránh rate limit...
2025-07-27 12:48:10,481 - INFO - 🔄 Xử lý batch 22 (offset: 8400)
2025-07-27 12:48:10,554 - INFO - 📊 Lấy được 400 records brand_office (offset: 8400, excluded: 8800)
2025-07-27 12:48:10,555 - INFO - 📊 Batch 22: 400 records
2025-07-27 12:48:10,732 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:48:11,167 - INFO - 🚀 Xử lý 230 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:48:11,811 - INFO - 📊 Progress: 5400 requests | 251.8 RPM | 100.0% success
2025-07-27 12:48:15,955 - INFO - 📊 Batch RPM: 2884.2 RPM (230 records trong 4.78s)
2025-07-27 12:48:15,956 - INFO - 🔄 Đã xử lý 8800 records...
2025-07-27 12:48:15,957 - INFO - ✅ Hoàn thành batch 22: 400 records
2025-07-27 12:48:15,964 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:48:15,964 - INFO - 😴 Xử lý batch trong 5.48s, nghỉ 55.51894211769104 giây để tránh rate limit...
2025-07-27 12:49:11,489 - INFO - 🔄 Xử lý batch 23 (offset: 8800)
2025-07-27 12:49:11,582 - INFO - 📊 Lấy được 400 records brand_office (offset: 8800, excluded: 9200)
2025-07-27 12:49:11,583 - INFO - 📊 Batch 23: 400 records
2025-07-27 12:49:11,790 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:49:12,216 - INFO - 🚀 Xử lý 236 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:49:12,846 - INFO - 📊 Progress: 5600 requests | 249.3 RPM | 100.0% success
2025-07-27 12:49:17,802 - INFO - 📊 Batch RPM: 2535.0 RPM (236 records trong 5.59s)
2025-07-27 12:49:17,803 - INFO - 🔄 Đã xử lý 9200 records...
2025-07-27 12:49:17,805 - INFO - ✅ Hoàn thành batch 23: 400 records
2025-07-27 12:49:17,812 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:49:17,813 - INFO - 😴 Xử lý batch trong 6.32s, nghỉ 54.68032622337341 giây để tránh rate limit...
2025-07-27 12:50:12,495 - INFO - 🔄 Xử lý batch 24 (offset: 9200)
2025-07-27 12:50:12,576 - INFO - 📊 Lấy được 400 records brand_office (offset: 9200, excluded: 9600)
2025-07-27 12:50:12,576 - INFO - 📊 Batch 24: 400 records
2025-07-27 12:50:12,675 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:50:13,142 - INFO - 🚀 Xử lý 241 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:50:13,647 - INFO - 📊 Progress: 5800 requests | 247.0 RPM | 100.0% success
2025-07-27 12:50:17,692 - INFO - 📊 Progress: 6000 requests | 254.8 RPM | 100.0% success
2025-07-27 12:50:18,806 - INFO - 📊 Batch RPM: 2553.2 RPM (241 records trong 5.66s)
2025-07-27 12:50:18,807 - INFO - 🔄 Đã xử lý 9600 records...
2025-07-27 12:50:18,809 - INFO - ✅ Hoàn thành batch 24: 400 records
2025-07-27 12:50:18,816 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:50:18,817 - INFO - 😴 Xử lý batch trong 6.32s, nghỉ 54.6809446811676 giây để tránh rate limit...
2025-07-27 12:51:13,502 - INFO - 🔄 Xử lý batch 25 (offset: 9600)
2025-07-27 12:51:13,596 - INFO - 📊 Lấy được 400 records brand_office (offset: 9600, excluded: 10000)
2025-07-27 12:51:13,599 - INFO - 📊 Batch 25: 400 records
2025-07-27 12:51:13,774 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:51:14,225 - INFO - 🚀 Xử lý 274 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:51:16,424 - INFO - 📊 Progress: 6200 requests | 252.8 RPM | 100.0% success
2025-07-27 12:51:22,510 - INFO - 📊 Batch RPM: 1984.4 RPM (274 records trong 8.28s)
2025-07-27 12:51:22,510 - INFO - 🔄 Đã xử lý 10000 records...
2025-07-27 12:51:22,512 - INFO - ✅ Hoàn thành batch 25: 400 records
2025-07-27 12:51:22,523 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:51:22,524 - INFO - 😴 Xử lý batch trong 9.02s, nghỉ 51.98056697845459 giây để tránh rate limit...
2025-07-27 12:52:14,507 - INFO - 🔄 Xử lý batch 26 (offset: 10000)
2025-07-27 12:52:14,584 - INFO - 📊 Lấy được 400 records brand_office (offset: 10000, excluded: 10400)
2025-07-27 12:52:14,585 - INFO - 📊 Batch 26: 400 records
2025-07-27 12:52:14,730 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:52:15,141 - INFO - 🚀 Xử lý 260 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:52:15,866 - INFO - 📊 Progress: 6400 requests | 250.8 RPM | 100.0% success
2025-07-27 12:52:23,753 - INFO - 📊 Batch RPM: 1811.5 RPM (260 records trong 8.61s)
2025-07-27 12:52:23,754 - INFO - 🔄 Đã xử lý 10400 records...
2025-07-27 12:52:23,755 - INFO - ✅ Hoàn thành batch 26: 400 records
2025-07-27 12:52:23,760 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:52:23,760 - INFO - 😴 Xử lý batch trong 9.25s, nghỉ 51.750434160232544 giây để tránh rate limit...
2025-07-27 12:53:15,514 - INFO - 🔄 Xử lý batch 27 (offset: 10400)
2025-07-27 12:53:15,608 - INFO - 📊 Lấy được 400 records brand_office (offset: 10400, excluded: 10800)
2025-07-27 12:53:15,609 - INFO - 📊 Batch 27: 400 records
2025-07-27 12:53:15,779 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:53:16,159 - INFO - 🚀 Xử lý 232 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:53:16,681 - INFO - 📊 Progress: 6600 requests | 248.8 RPM | 100.0% success
2025-07-27 12:53:20,474 - INFO - 📊 Batch RPM: 3226.6 RPM (232 records trong 4.31s)
2025-07-27 12:53:20,474 - INFO - 🔄 Đã xử lý 10800 records...
2025-07-27 12:53:20,475 - INFO - ✅ Hoàn thành batch 27: 400 records
2025-07-27 12:53:20,480 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:53:20,480 - INFO - 😴 Xử lý batch trong 4.96s, nghỉ 56.035341024398804 giây để tránh rate limit...
2025-07-27 12:54:16,518 - INFO - 🔄 Xử lý batch 28 (offset: 10800)
2025-07-27 12:54:16,614 - INFO - 📊 Lấy được 400 records brand_office (offset: 10800, excluded: 11200)
2025-07-27 12:54:16,614 - INFO - 📊 Batch 28: 400 records
2025-07-27 12:54:16,761 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:54:17,182 - INFO - 🚀 Xử lý 281 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:54:17,649 - INFO - 📊 Progress: 6800 requests | 246.9 RPM | 100.0% success
2025-07-27 12:54:20,674 - INFO - 📊 Progress: 7000 requests | 253.7 RPM | 100.0% success
2025-07-27 12:54:25,672 - INFO - 📊 Batch RPM: 1985.8 RPM (281 records trong 8.49s)
2025-07-27 12:54:25,673 - INFO - 🔄 Đã xử lý 11200 records...
2025-07-27 12:54:25,674 - INFO - ✅ Hoàn thành batch 28: 400 records
2025-07-27 12:54:25,679 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:54:25,679 - INFO - 😴 Xử lý batch trong 9.16s, nghỉ 51.84006714820862 giây để tránh rate limit...
2025-07-27 12:55:17,525 - INFO - 🔄 Xử lý batch 29 (offset: 11200)
2025-07-27 12:55:17,636 - INFO - 📊 Lấy được 400 records brand_office (offset: 11200, excluded: 11600)
2025-07-27 12:55:17,637 - INFO - 📊 Batch 29: 400 records
2025-07-27 12:55:17,834 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:55:18,197 - INFO - 🚀 Xử lý 243 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:55:19,187 - INFO - 📊 Progress: 7200 requests | 252.0 RPM | 100.0% success
2025-07-27 12:55:22,832 - INFO - 📊 Batch RPM: 3145.8 RPM (243 records trong 4.63s)
2025-07-27 12:55:22,832 - INFO - 🔄 Đã xử lý 11600 records...
2025-07-27 12:55:22,834 - INFO - ✅ Hoàn thành batch 29: 400 records
2025-07-27 12:55:22,846 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:55:22,846 - INFO - 😴 Xử lý batch trong 5.32s, nghỉ 55.68249702453613 giây để tránh rate limit...
2025-07-27 12:56:18,560 - INFO - 🔄 Xử lý batch 30 (offset: 11600)
2025-07-27 12:56:18,655 - INFO - 📊 Lấy được 400 records brand_office (offset: 11600, excluded: 12000)
2025-07-27 12:56:18,656 - INFO - 📊 Batch 30: 400 records
2025-07-27 12:56:18,835 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:56:19,211 - INFO - 🚀 Xử lý 219 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:56:19,905 - INFO - 📊 Progress: 7400 requests | 250.2 RPM | 100.0% success
2025-07-27 12:56:22,711 - INFO - 📊 Batch RPM: 3754.8 RPM (219 records trong 3.50s)
2025-07-27 12:56:22,711 - INFO - 🔄 Đã xử lý 12000 records...
2025-07-27 12:56:22,712 - INFO - ✅ Hoàn thành batch 30: 400 records
2025-07-27 12:56:22,724 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:56:22,724 - INFO - 😴 Xử lý batch trong 4.16s, nghỉ 56.83909487724304 giây để tránh rate limit...
2025-07-27 12:57:19,660 - INFO - 🔄 Xử lý batch 31 (offset: 12000)
2025-07-27 12:57:19,760 - INFO - 📊 Lấy được 400 records brand_office (offset: 12000, excluded: 12400)
2025-07-27 12:57:19,761 - INFO - 📊 Batch 31: 400 records
2025-07-27 12:57:19,922 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:57:20,373 - INFO - 🚀 Xử lý 238 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:57:21,028 - INFO - 📊 Progress: 7600 requests | 248.4 RPM | 100.0% success
2025-07-27 12:57:26,336 - INFO - 📊 Batch RPM: 2394.7 RPM (238 records trong 5.96s)
2025-07-27 12:57:26,337 - INFO - 🔄 Đã xử lý 12400 records...
2025-07-27 12:57:26,337 - INFO - ✅ Hoàn thành batch 31: 400 records
2025-07-27 12:57:26,346 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:57:26,346 - INFO - 😴 Xử lý batch trong 6.68s, nghỉ 54.316747188568115 giây để tránh rate limit...
2025-07-27 13:14:32,269 - INFO - 🔄 Xử lý batch 32 (offset: 12400)
2025-07-27 13:14:32,365 - INFO - 📊 Lấy được 400 records brand_office (offset: 12400, excluded: 12800)
2025-07-27 13:14:32,366 - INFO - 📊 Batch 32: 400 records
2025-07-27 13:14:32,522 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 13:14:34,284 - INFO - 🚀 Xử lý 247 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 13:14:35,754 - INFO - 📊 Progress: 7800 requests | 163.0 RPM | 100.0% success
2025-07-27 13:14:38,593 - INFO - 📊 Progress: 8000 requests | 167.0 RPM | 100.0% success
2025-07-27 13:14:39,066 - INFO - 📊 Batch RPM: 3100.2 RPM (247 records trong 4.78s)
2025-07-27 13:14:39,067 - INFO - 🔄 Đã xử lý 12800 records...
2025-07-27 13:14:39,067 - INFO - ✅ Hoàn thành batch 32: 400 records
2025-07-27 13:14:39,080 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 13:14:39,080 - INFO - 😴 Xử lý batch trong 6.81s, nghỉ 54.19095492362976 giây để tránh rate limit...
2025-07-27 13:31:38,091 - INFO - 🔄 Xử lý batch 33 (offset: 12800)
2025-07-27 13:31:38,171 - INFO - 📊 Lấy được 0 records brand_office (offset: 12800, excluded: 13200)
2025-07-27 13:31:38,173 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 13:31:38,173 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 13:31:38,173 - INFO - 📊 Tổng xử lý: 12800
2025-07-27 13:31:38,173 - INFO - 📊 Matched: 8021
2025-07-27 13:31:38,173 - INFO - 📊 Unmatched: 4779
2025-07-27 13:31:38,173 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 13:37:25,024 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 13:37:25,057 - INFO - ✅ Kết nối database thành công!
2025-07-27 13:37:25,058 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 13:37:26,163 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 13:37:30,016 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 13:37:30,017 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 13:37:30,085 - INFO - 📊 Đã đọc 13200 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 13:37:30,161 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 13:37:30,162 - INFO - ============================================================
2025-07-27 13:37:30,162 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 13:37:30,162 - INFO - ============================================================
2025-07-27 13:37:30,162 - INFO - 📊 Tổng records: 25989
2025-07-27 13:37:30,162 - INFO - 📊 Đã xử lý: 13200
2025-07-27 13:37:30,162 - INFO - 📊 Còn lại: 12789
2025-07-27 13:37:30,162 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 13:37:30,162 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 13:37:30,178 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 13200)
2025-07-27 13:37:30,178 - INFO - 📊 Batch 1: 400 records
2025-07-27 13:37:30,242 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 13:37:30,243 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed
2025-07-27 13:40:44,939 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 13:40:44,975 - INFO - ✅ Kết nối database thành công!
2025-07-27 13:40:44,976 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 13:40:45,807 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 13:40:49,710 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 13:40:49,711 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 13:40:49,783 - INFO - 📊 Đã đọc 13200 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 13:40:49,847 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 13:40:49,847 - INFO - ============================================================
2025-07-27 13:40:49,847 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 13:40:49,847 - INFO - ============================================================
2025-07-27 13:40:49,847 - INFO - 📊 Tổng records: 25989
2025-07-27 13:40:49,847 - INFO - 📊 Đã xử lý: 13200
2025-07-27 13:40:49,847 - INFO - 📊 Còn lại: 12789
2025-07-27 13:40:49,847 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 13:40:49,847 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 13:40:49,862 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 13200)
2025-07-27 13:40:49,862 - INFO - 📊 Batch 1: 400 records
2025-07-27 13:40:49,921 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 13:40:49,922 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed
2025-07-27 13:40:58,465 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 13:40:58,481 - INFO - ✅ Kết nối database thành công!
2025-07-27 13:40:58,482 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 13:40:59,201 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 13:41:03,014 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 13:41:03,015 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 13:41:03,095 - INFO - 📊 Đã đọc 13200 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 13:41:03,160 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 13:41:03,160 - INFO - ============================================================
2025-07-27 13:41:03,160 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 13:41:03,160 - INFO - ============================================================
2025-07-27 13:41:03,160 - INFO - 📊 Tổng records: 25989
2025-07-27 13:41:03,160 - INFO - 📊 Đã xử lý: 13200
2025-07-27 13:41:03,160 - INFO - 📊 Còn lại: 12789
2025-07-27 13:41:03,160 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 13:41:03,160 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 13:41:03,178 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 13200)
2025-07-27 13:41:03,178 - INFO - 📊 Batch 1: 400 records
2025-07-27 13:41:03,239 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 13:41:03,239 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed

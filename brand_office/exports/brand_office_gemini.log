2025-07-27 12:20:26,882 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:20:26,882 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:20:26,930 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:20:26,931 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:20:27,644 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:20:31,674 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:20:31,675 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:20:31,692 - INFO - 📄 File exports/brand_offices_updated.csv chưa tồn tại, bắt đầu từ đầu
2025-07-27 12:20:31,886 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:20:31,886 - INFO - ============================================================
2025-07-27 12:20:31,886 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:20:31,886 - INFO - ============================================================
2025-07-27 12:20:31,886 - INFO - 📊 Tổng records: 25989
2025-07-27 12:20:31,886 - INFO - 📊 Đã xử lý: 0
2025-07-27 12:20:31,886 - INFO - 📊 Còn lại: 25989
2025-07-27 12:20:31,886 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:20:31,886 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:20:31,888 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 0)
2025-07-27 12:20:31,889 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:20:31,892 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:31,892 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,895 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:31,896 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:31,898 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:31,899 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.668 10.8, 106.668 10.8, 106.668 10.8, 106.668 10.8, 106...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Nhuận', 'code': '27073'}
2025-07-27 12:20:31,900 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:31,900 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.668 10.8, 106.668 10.8, 106.668 10.8, 106.668 10.8, 106...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Nhuận', 'code': '27073'}
2025-07-27 12:20:31,900 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.66 10.764, 106.66 10.763, 106.66 10.762, 106.661 10.761...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Minh Phụng', 'code': '27238'}
2025-07-27 12:20:31,901 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.636 10.769, 106.636 10.769, 106.638 10.769, 106.64 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Thới', 'code': '27232'}
2025-07-27 12:20:31,901 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,902 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,902 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,903 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,904 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,904 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,904 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:31,905 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,905 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.684 10.777, 106.685 10.776, 106.685 10.776, 106.685 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bàn Cờ', 'code': '27154'}
2025-07-27 12:20:31,909 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.205 13.781, 109.205 13.781, 109.205 13.781, 109.205 13....>, 'geo_province_code': '52', 'province_title': 'Tỉnh Gia Lai', 'ward_title': 'Phường Quy Nhơn Nam', 'code': '21592'}
2025-07-27 12:20:31,910 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.103 10.943, 108.104 10.942, 108.104 10.942, 108.104 10....>, 'geo_province_code': '68', 'province_title': 'Tỉnh Lâm Đồng', 'ward_title': 'Phường Phan Thiết', 'code': '22945'}
2025-07-27 12:20:31,913 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:31,915 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:31,916 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.203 16.064, 108.203 16.064, 108.204 16.063, 108.204 16....>, 'geo_province_code': '48', 'province_title': 'Thành phố Đà Nẵng', 'ward_title': 'Phường Hòa Cường', 'code': '20257'}
2025-07-27 12:20:31,921 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.159 12.251, 109.16 12.251, 109.16 12.251, 109.161 12.25...>, 'geo_province_code': '56', 'province_title': 'Tỉnh Khánh Hòa', 'ward_title': 'Phường Nam Nha Trang', 'code': '22402'}
2025-07-27 12:20:31,924 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:31,925 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:31,925 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.685 10.768, 106.685 10.768, 106.686 10.768, 106.686 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Cầu Ông Lãnh', 'code': '26758'}
2025-07-27 12:20:31,926 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.636 10.769, 106.636 10.769, 106.638 10.769, 106.64 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Thới', 'code': '27232'}
2025-07-27 12:20:31,927 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.638 10.796, 106.638 10.796, 106.639 10.795, 106.639 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Phú', 'code': '27031'}
2025-07-27 12:20:31,928 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.827 10.76, 106.827 10.76, 106.827 10.76, 106.829 10.761...>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Xã Đại Phước', 'code': '26491'}
2025-07-27 12:20:31,929 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,931 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:31,931 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:31,932 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.661 10.742, 106.661 10.742, 106.661 10.742, 106.661 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Đông', 'code': '27424'}
2025-07-27 12:20:31,932 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,933 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.62 10.785, 106.621 10.785, 106.621 10.785, 106.621 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Thạnh', 'code': '27028'}
2025-07-27 12:20:31,944 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((107.542 16.482, 107.542 16.482, 107.542 16.482, 107.542 16....>, 'geo_province_code': '46', 'province_title': 'Thành phố Huế', 'ward_title': 'Phường Kim Long', 'code': '19774'}
2025-07-27 12:20:31,945 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,945 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.837 21.011, 105.837 21.011, 105.837 21.011, 105.838 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Kim Liên', 'code': '00229'}
2025-07-27 12:20:31,946 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,947 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:31,949 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:31,951 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:31,952 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,953 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.69 10.751, 106.69 10.75, 106.69 10.75, 106.69 10.75, 10...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Chánh Hưng', 'code': '27418'}
2025-07-27 12:20:31,953 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.698 10.76, 106.701 10.756, 106.701 10.756, 106.702 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vĩnh Hội', 'code': '27286'}
2025-07-27 12:20:31,954 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.675 10.761, 106.675 10.76, 106.676 10.759, 106.676 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường An Đông', 'code': '27316'}
2025-07-27 12:20:31,954 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.698 10.76, 106.701 10.756, 106.701 10.756, 106.702 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vĩnh Hội', 'code': '27286'}
2025-07-27 12:20:31,955 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.636 10.769, 106.636 10.769, 106.638 10.769, 106.64 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Thới', 'code': '27232'}
2025-07-27 12:20:31,955 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.684 10.777, 106.685 10.776, 106.685 10.776, 106.685 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bàn Cờ', 'code': '27154'}
2025-07-27 12:20:31,955 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:31,956 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.613 10.781, 106.613 10.78, 106.613 10.78, 106.613 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Trị Đông', 'code': '27448'}
2025-07-27 12:20:31,957 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.705 10.792, 106.706 10.792, 106.706 10.792, 106.706 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Sài Gòn', 'code': '26740'}
2025-07-27 12:20:31,957 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.705 10.792, 106.706 10.792, 106.706 10.792, 106.706 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Sài Gòn', 'code': '26740'}
2025-07-27 12:20:31,957 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,958 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,958 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.638 10.796, 106.638 10.796, 106.639 10.795, 106.639 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Phú', 'code': '27031'}
2025-07-27 12:20:31,959 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.649 10.809, 106.649 10.809, 106.65 10.809, 106.651 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bảy Hiền', 'code': '26983'}
2025-07-27 12:20:31,960 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.718 10.814, 106.718 10.814, 106.718 10.814, 106.718 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Thạnh Mỹ Tây', 'code': '26956'}
2025-07-27 12:20:31,961 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:31,962 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:31,963 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.691 10.811, 106.691 10.811, 106.691 10.811, 106.692 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Gia Định', 'code': '26944'}
2025-07-27 12:20:31,964 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:31,964 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:31,965 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,965 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:31,966 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:31,966 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:31,968 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.822 21.093, 105.826 21.092, 105.831 21.09, 105.831 21.0...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hồng Hà', 'code': '00097'}
2025-07-27 12:20:31,969 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,970 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:31,970 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:31,970 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:31,971 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,972 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,972 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:31,973 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.682 10.765, 106.682 10.765, 106.682 10.765, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Chợ Quán', 'code': '27301'}
2025-07-27 12:20:31,973 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.653 10.79, 106.653 10.79, 106.653 10.79, 106.653 10.789...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Hòa', 'code': '26995'}
2025-07-27 12:20:31,974 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.638 10.796, 106.638 10.796, 106.639 10.795, 106.639 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Phú', 'code': '27031'}
2025-07-27 12:20:31,975 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,975 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:31,976 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:31,977 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 20.988, 105.827 20.987, 105.827 20.987, 105.827 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Định Công', 'code': '00316'}
2025-07-27 12:20:31,978 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,978 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.891 21.051, 105.891 21.051, 105.892 21.051, 105.892 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Long Biên', 'code': '00145'}
2025-07-27 12:20:31,979 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,979 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.851 21.041, 105.851 21.04, 105.851 21.04, 105.851 21.04...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hoàn Kiếm', 'code': '00070'}
2025-07-27 12:20:31,980 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.77 20.965, 105.77 20.965, 105.77 20.965, 105.77 20.964,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Kiến Hưng', 'code': '09552'}
2025-07-27 12:20:31,981 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.851 21.041, 105.851 21.04, 105.851 21.04, 105.851 21.04...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hoàn Kiếm', 'code': '00070'}
2025-07-27 12:20:31,982 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.685 10.768, 106.685 10.768, 106.686 10.768, 106.686 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Cầu Ông Lãnh', 'code': '26758'}
2025-07-27 12:20:31,982 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,982 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:31,983 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.682 10.791, 106.682 10.791, 106.682 10.791, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Nhiêu Lộc', 'code': '27142'}
2025-07-27 12:20:31,983 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.701 10.765, 106.701 10.765, 106.702 10.764, 106.704 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Khánh Hội', 'code': '27265'}
2025-07-27 12:20:31,984 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,984 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.691 10.811, 106.691 10.811, 106.691 10.811, 106.692 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Gia Định', 'code': '26944'}
2025-07-27 12:20:31,985 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.668 10.8, 106.668 10.8, 106.668 10.8, 106.668 10.8, 106...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Nhuận', 'code': '27073'}
2025-07-27 12:20:31,986 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.684 10.834, 106.684 10.834, 106.684 10.834, 106.685 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hạnh Thông', 'code': '26890'}
2025-07-27 12:20:31,986 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.653 10.79, 106.653 10.79, 106.653 10.79, 106.653 10.789...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Hòa', 'code': '26995'}
2025-07-27 12:20:31,988 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:31,989 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.649 10.997, 106.65 10.997, 106.65 10.997, 106.651 10.99...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Thủ Dầu Một', 'code': '25747'}
2025-07-27 12:20:31,992 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.802 10.977, 106.802 10.977, 106.802 10.977, 106.802 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Trấn Biên', 'code': '26041'}
2025-07-27 12:20:31,997 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.159 12.251, 109.16 12.251, 109.16 12.251, 109.161 12.25...>, 'geo_province_code': '56', 'province_title': 'Tỉnh Khánh Hòa', 'ward_title': 'Phường Nam Nha Trang', 'code': '22402'}
2025-07-27 12:20:31,998 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.603 10.826, 106.604 10.826, 106.604 10.826, 106.606 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hưng Hòa', 'code': '27439'}
2025-07-27 12:20:31,998 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.62 10.785, 106.621 10.785, 106.621 10.785, 106.621 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Thạnh', 'code': '27028'}
2025-07-27 12:20:31,999 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.664 10.777, 106.667 10.768, 106.667 10.768, 106.669 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Diên Hồng', 'code': '27169'}
2025-07-27 12:20:31,999 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,999 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.698 10.76, 106.701 10.756, 106.701 10.756, 106.702 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vĩnh Hội', 'code': '27286'}
2025-07-27 12:20:32,000 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,001 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:32,002 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,002 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:32,003 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.891 21.051, 105.891 21.051, 105.892 21.051, 105.892 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Long Biên', 'code': '00145'}
2025-07-27 12:20:32,004 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.842 21.029, 105.843 21.028, 105.843 21.028, 105.843 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Cửa Nam', 'code': '00082'}
2025-07-27 12:20:32,004 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.121 9.208, 105.122 9.207, 105.123 9.206, 105.123 9.206,...>, 'geo_province_code': '96', 'province_title': 'Tỉnh Cà Mau', 'ward_title': 'Phường Lý Văn Lâm', 'code': '32014'}
2025-07-27 12:20:32,005 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.203 16.064, 108.203 16.064, 108.204 16.063, 108.204 16....>, 'geo_province_code': '48', 'province_title': 'Thành phố Đà Nẵng', 'ward_title': 'Phường Hòa Cường', 'code': '20257'}
2025-07-27 12:20:32,006 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.753 10.046, 105.753 10.046, 105.754 10.046, 105.755 10....>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Tân An', 'code': '31147'}
2025-07-27 12:20:32,009 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,009 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:32,010 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,011 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.66 18.697, 105.661 18.697, 105.661 18.696, 105.661 18.6...>, 'geo_province_code': '40', 'province_title': 'Tỉnh Nghệ An', 'ward_title': 'Phường Thành Vinh', 'code': '16681'}
2025-07-27 12:20:32,012 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:32,015 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.802 10.977, 106.802 10.977, 106.802 10.977, 106.802 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Trấn Biên', 'code': '26041'}
2025-07-27 12:20:32,018 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:32,018 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,019 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,019 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,019 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,020 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:32,021 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:32,022 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,022 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:32,023 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:32,023 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,031 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.86 18.344, 105.86 18.344, 105.861 18.344, 105.861 18.34...>, 'geo_province_code': '42', 'province_title': 'Tỉnh Hà Tĩnh', 'ward_title': 'Phường Hà Huy Tập', 'code': '18652'}
2025-07-27 12:20:32,032 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.467 9.784, 105.467 9.784, 105.467 9.784, 105.468 9.784,...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Vị Thanh', 'code': '31321'}
2025-07-27 12:20:32,032 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.995 10.264, 105.995 10.264, 105.996 10.264, 105.996 10....>, 'geo_province_code': '86', 'province_title': 'Tỉnh Vĩnh Long', 'ward_title': 'Phường Thanh Đức', 'code': '29590'}
2025-07-27 12:20:32,038 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((104.843 21.713, 104.845 21.712, 104.849 21.712, 104.852 21....>, 'geo_province_code': '15', 'province_title': 'Tỉnh Lào Cai', 'ward_title': 'Phường Âu Lâu', 'code': '04543'}
2025-07-27 12:20:32,046 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.097 12.696, 108.097 12.696, 108.097 12.696, 108.097 12....>, 'geo_province_code': '66', 'province_title': 'Tỉnh Đắk Lắk', 'ward_title': 'Phường Tân Lập', 'code': '24121'}
2025-07-27 12:20:32,047 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.649 10.997, 106.65 10.997, 106.65 10.997, 106.651 10.99...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Thủ Dầu Một', 'code': '25747'}
2025-07-27 12:20:32,049 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:32,049 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,050 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.822 21.093, 105.826 21.092, 105.831 21.09, 105.831 21.0...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hồng Hà', 'code': '00097'}
2025-07-27 12:20:32,051 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,052 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:32,054 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:32,057 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,058 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:32,058 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:32,061 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.775 10.017, 105.776 10.016, 105.776 10.016, 105.777 10....>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Cái Răng', 'code': '31186'}
2025-07-27 12:20:32,062 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,062 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,063 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:32,066 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,066 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,066 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,070 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,070 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,071 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,071 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:32,072 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.661 10.742, 106.661 10.742, 106.661 10.742, 106.661 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Đông', 'code': '27424'}
2025-07-27 12:20:32,073 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:32,073 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.603 10.826, 106.604 10.826, 106.604 10.826, 106.606 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hưng Hòa', 'code': '27439'}
2025-07-27 12:20:32,073 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.644 10.811, 106.644 10.811, 106.645 10.81, 106.645 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Bình', 'code': '27004'}
2025-07-27 12:20:32,074 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,074 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,075 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.715 10.738, 106.716 10.738, 106.717 10.738, 106.72 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Mỹ', 'code': '27487'}
2025-07-27 12:20:32,076 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.859 21.009, 105.859 21.009, 105.86 21.009, 105.861 21.0...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Bạch Mai', 'code': '00292'}
2025-07-27 12:20:32,076 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,077 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.203 16.064, 108.203 16.064, 108.204 16.063, 108.204 16....>, 'geo_province_code': '48', 'province_title': 'Thành phố Đà Nẵng', 'ward_title': 'Phường Hòa Cường', 'code': '20257'}
2025-07-27 12:20:32,082 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.159 12.251, 109.16 12.251, 109.16 12.251, 109.161 12.25...>, 'geo_province_code': '56', 'province_title': 'Tỉnh Khánh Hòa', 'ward_title': 'Phường Nam Nha Trang', 'code': '22402'}
2025-07-27 12:20:32,083 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:32,084 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.842 21.029, 105.843 21.028, 105.843 21.028, 105.843 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Cửa Nam', 'code': '00082'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.811 21.01, 105.811 21.01, 105.811 21.01, 105.813 21.009...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Thanh Xuân', 'code': '00367'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,086 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,087 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,087 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,087 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,087 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:20:33,718 - INFO - 🚀 Xử lý 400 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:36,209 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:21:36,209 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:21:36,245 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:21:36,245 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:21:36,594 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:21:41,093 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:21:41,095 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:21:41,111 - INFO - 📄 File exports/brand_offices_updated.csv chưa tồn tại, bắt đầu từ đầu
2025-07-27 12:21:41,273 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:21:41,273 - INFO - ============================================================
2025-07-27 12:21:41,273 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:21:41,273 - INFO - ============================================================
2025-07-27 12:21:41,273 - INFO - 📊 Tổng records: 25989
2025-07-27 12:21:41,273 - INFO - 📊 Đã xử lý: 0
2025-07-27 12:21:41,273 - INFO - 📊 Còn lại: 25989
2025-07-27 12:21:41,273 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:21:41,273 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:21:41,275 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 0)
2025-07-27 12:21:41,275 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:21:41,347 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:21:42,806 - INFO - 🚀 Xử lý 268 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:45,640 - INFO - 📊 Progress: 200 requests | 1272.4 RPM | 100.0% success
2025-07-27 12:21:48,715 - INFO - 📊 Batch RPM: 2721.3 RPM (268 records trong 5.91s)
2025-07-27 12:21:48,715 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 12:21:48,715 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 12:21:48,720 - INFO - ✅ Đã tạo file mới và lưu 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:21:48,720 - INFO - 😴 Xử lý batch trong 7.45s, nghỉ 53.553420066833496 giây để tránh rate limit...
2025-07-27 12:21:48,720 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 12:21:48,759 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 400)
2025-07-27 12:21:48,759 - INFO - 📊 Batch 2: 400 records
2025-07-27 12:21:48,819 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:21:49,400 - INFO - 🚀 Xử lý 271 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:51,520 - INFO - 📊 Progress: 400 requests | 1567.5 RPM | 100.0% success
2025-07-27 12:21:55,101 - INFO - 📊 Batch RPM: 2852.3 RPM (271 records trong 5.70s)
2025-07-27 12:21:55,101 - INFO - 🔄 Đã xử lý 800 records...
2025-07-27 12:21:55,101 - INFO - ✅ Hoàn thành batch 2: 400 records
2025-07-27 12:21:55,105 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:21:55,105 - INFO - 😴 Xử lý batch trong 6.38s, nghỉ 54.6150860786438 giây để tránh rate limit...
2025-07-27 12:21:55,105 - INFO - 🔄 Xử lý batch 3 (offset: 800)
2025-07-27 12:21:55,131 - INFO - 📊 Lấy được 400 records brand_office (offset: 800, excluded: 800)
2025-07-27 12:21:55,131 - INFO - 📊 Batch 3: 400 records
2025-07-27 12:21:55,192 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:21:55,586 - INFO - 🚀 Xử lý 267 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:56,156 - INFO - 📊 Progress: 600 requests | 1804.8 RPM | 100.0% success
2025-07-27 12:22:00,847 - INFO - 📊 Progress: 800 requests | 1948.2 RPM | 100.0% success
2025-07-27 12:22:00,884 - INFO - 📊 Batch RPM: 3023.8 RPM (267 records trong 5.30s)
2025-07-27 12:22:00,884 - INFO - 🔄 Đã xử lý 1200 records...
2025-07-27 12:22:00,884 - INFO - ✅ Hoàn thành batch 3: 400 records
2025-07-27 12:22:00,888 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:22:00,888 - INFO - 😴 Xử lý batch trong 5.78s, nghỉ 55.21640396118164 giây để tránh rate limit...
2025-07-27 12:22:00,888 - INFO - 🔄 Xử lý batch 4 (offset: 1200)
2025-07-27 12:22:00,917 - INFO - 📊 Lấy được 400 records brand_office (offset: 1200, excluded: 1200)
2025-07-27 12:22:00,917 - INFO - 📊 Batch 4: 400 records
2025-07-27 12:22:00,997 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:22:01,378 - INFO - 🚀 Xử lý 255 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:22:02,603 - INFO - 📊 Progress: 1000 requests | 2273.3 RPM | 100.0% success
2025-07-27 12:22:03,400 - INFO - 📊 Batch RPM: 7566.3 RPM (255 records trong 2.02s)
2025-07-27 12:22:03,401 - INFO - 🔄 Đã xử lý 1600 records...
2025-07-27 12:22:03,401 - INFO - ✅ Hoàn thành batch 4: 400 records
2025-07-27 12:22:03,404 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:22:03,404 - INFO - 😴 Xử lý batch trong 2.52s, nghỉ 58.48449206352234 giây để tránh rate limit...
2025-07-27 12:22:03,404 - INFO - 🔄 Xử lý batch 5 (offset: 1600)
2025-07-27 12:22:03,432 - INFO - 📊 Lấy được 400 records brand_office (offset: 1600, excluded: 1600)
2025-07-27 12:22:03,433 - INFO - 📊 Batch 5: 400 records
2025-07-27 12:22:03,706 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:22:04,118 - INFO - 🚀 Xử lý 229 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14285: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14277: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14329: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14322: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,157 - ERROR - ❌ Error processing record 14323: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,158 - ERROR - ❌ Error processing record 14280: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,161 - ERROR - ❌ Error processing record 14279: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,161 - ERROR - ❌ Error processing record 14272: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,162 - ERROR - ❌ Error processing record 14340: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,162 - ERROR - ❌ Error processing record 14282: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,162 - ERROR - ❌ Error processing record 14348: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14309: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14337: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14301: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14350: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14327: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14317: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14304: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14319: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14303: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14336: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,164 - ERROR - ❌ Error processing record 14305: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,164 - ERROR - ❌ Error processing record 14332: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,241 - ERROR - ❌ Error processing record 14366: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,246 - ERROR - ❌ Error processing record 14363: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,261 - ERROR - ❌ Error processing record 14368: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,293 - ERROR - ❌ Error processing record 14370: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,306 - INFO - 📊 Progress: 1200 requests | 2474.5 RPM | 100.0% success
2025-07-27 12:22:05,324 - ERROR - ❌ Error processing record 14385: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,326 - ERROR - ❌ Error processing record 14375: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,326 - ERROR - ❌ Error processing record 14371: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,327 - ERROR - ❌ Error processing record 14380: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,330 - ERROR - ❌ Error processing record 14379: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,356 - ERROR - ❌ Error processing record 14388: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,386 - ERROR - ❌ Error processing record 14389: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,397 - ERROR - ❌ Error processing record 14391: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,409 - ERROR - ❌ Error processing record 14392: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,415 - ERROR - ❌ Error processing record 14393: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,416 - ERROR - ❌ Error processing record 14395: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,418 - ERROR - ❌ Error processing record 14400: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,421 - ERROR - ❌ Error processing record 14402: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,425 - ERROR - ❌ Error processing record 14406: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,428 - ERROR - ❌ Error processing record 14405: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,448 - ERROR - ❌ Error processing record 14407: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,449 - ERROR - ❌ Error processing record 14409: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,452 - ERROR - ❌ Error processing record 14408: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,452 - ERROR - ❌ Error processing record 14410: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,453 - ERROR - ❌ Error processing record 14411: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,950 - INFO - 📊 Batch RPM: 7502.9 RPM (229 records trong 1.83s)
2025-07-27 12:22:05,950 - INFO - 🔄 Đã xử lý 2000 records...
2025-07-27 12:22:05,951 - INFO - ✅ Hoàn thành batch 5: 400 records
2025-07-27 12:22:05,956 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:22:05,956 - INFO - 😴 Xử lý batch trong 2.55s, nghỉ 58.44778108596802 giây để tránh rate limit...
2025-07-27 12:22:05,956 - INFO - 🔄 Xử lý batch 6 (offset: 2000)
2025-07-27 12:22:05,985 - INFO - 📊 Lấy được 400 records brand_office (offset: 2000, excluded: 2000)
2025-07-27 12:22:05,985 - INFO - 📊 Batch 6: 400 records
2025-07-27 12:22:06,198 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:22:06,437 - ERROR - ❌ Lỗi trong quá trình xử lý: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 53
}
]
2025-07-27 12:24:51,690 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:24:51,690 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:24:51,730 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:24:51,730 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:24:52,092 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:25:04,673 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:25:04,673 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:25:04,692 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:25:04,693 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:25:05,030 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:25:08,881 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:25:08,882 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:25:08,896 - INFO - 📄 File exports/brand_offices_updated.csv chưa tồn tại, bắt đầu từ đầu
2025-07-27 12:25:08,988 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:25:08,989 - INFO - ============================================================
2025-07-27 12:25:08,989 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:25:08,989 - INFO - ============================================================
2025-07-27 12:25:08,989 - INFO - 📊 Tổng records: 25989
2025-07-27 12:25:08,989 - INFO - 📊 Đã xử lý: 0
2025-07-27 12:25:08,989 - INFO - 📊 Còn lại: 25989
2025-07-27 12:25:08,989 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:25:08,989 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:25:08,991 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 0)
2025-07-27 12:25:08,991 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:25:09,057 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:25:20,643 - INFO - 🚀 Xử lý 268 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:25:23,422 - INFO - 📊 Progress: 200 requests | 640.0 RPM | 100.0% success
2025-07-27 12:25:27,136 - INFO - 📊 Batch RPM: 2476.8 RPM (268 records trong 6.49s)
2025-07-27 12:25:27,136 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 12:25:27,136 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 12:25:27,148 - INFO - ✅ Đã tạo file mới và lưu 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:25:27,148 - INFO - 😴 Xử lý batch trong 18.16s, nghỉ 42.84077787399292 giây để tránh rate limit...
2025-07-27 12:26:09,990 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 12:26:10,020 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 400)
2025-07-27 12:26:10,021 - INFO - 📊 Batch 2: 400 records
2025-07-27 12:26:10,088 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:26:10,620 - INFO - 🚀 Xử lý 271 records với Gemini concurrent (max 100 đồng thời)...
